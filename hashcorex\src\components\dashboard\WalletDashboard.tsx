'use client';

import React, { useState, useEffect } from 'react';
import { Button, Input, Card, CardHeader, CardTitle, CardContent, Modal } from '@/components/ui';
import { Grid } from '@/components/layout';
import { Wallet, ArrowUpRight, ArrowDownLeft, Clock, CheckCircle, XCircle, Copy, DollarSign, Search, Filter, RefreshCw } from 'lucide-react';
import { formatCurrency, formatDateTime, copyToClipboard } from '@/lib/utils';
import { DepositPage } from '@/components/wallet/DepositPage';

interface WalletData {
  balance: number;
  pendingEarnings: number;
  recentTransactions: Array<{
    id: string;
    type: string;
    amount: number;
    description: string;
    status: string;
    createdAt: string;
  }>;
}

interface TransactionData {
  id: string;
  type: string;
  category: string;
  amount: number;
  description: string;
  status: string;
  reference?: string;
  createdAt: string;
  // Additional details for modal
  txid?: string | null;
  usdtAddress?: string | null;
  rejectionReason?: string | null;
  processedAt?: string | null;
  confirmations?: number;
  blockNumber?: string;
  senderAddress?: string;
}



interface WithdrawalSettings {
  minWithdrawalAmount: number;
  fixedFee: number;
  percentageFee: number;
  processingDays: number;
}

export const WalletDashboard: React.FC = () => {
  const [activeTab, setActiveTab] = useState('overview');
  const [walletData, setWalletData] = useState<WalletData | null>(null);
  const [withdrawalSettings, setWithdrawalSettings] = useState<WithdrawalSettings | null>(null);
  const [loading, setLoading] = useState(true);
  const [showWithdrawModal, setShowWithdrawModal] = useState(false);
  const [withdrawalForm, setWithdrawalForm] = useState({
    amount: '',
    usdtAddress: '',
  });
  const [withdrawalLoading, setWithdrawalLoading] = useState(false);
  const [withdrawalError, setWithdrawalError] = useState('');

  // Transaction filtering states
  const [allTransactions, setAllTransactions] = useState<TransactionData[]>([]);
  const [transactionLoading, setTransactionLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState('ALL');
  const [filterStatus, setFilterStatus] = useState('ALL');
  const [showFilters, setShowFilters] = useState(false);
  const [transactionTypes, setTransactionTypes] = useState<string[]>([]);
  const [statusOptions, setStatusOptions] = useState<string[]>([]);

  // Transaction detail modal states
  const [selectedTransaction, setSelectedTransaction] = useState<TransactionData | null>(null);
  const [showTransactionModal, setShowTransactionModal] = useState(false);

  useEffect(() => {
    fetchWalletData();
    fetchWithdrawalSettings();
    fetchAllTransactions();

    // Set up automatic refresh every 30 seconds
    const refreshInterval = setInterval(() => {
      fetchWalletData();
      fetchAllTransactions();
    }, 30000);

    return () => {
      clearInterval(refreshInterval);
    };
  }, []);

  // Fetch transactions when filters change
  useEffect(() => {
    fetchAllTransactions();
  }, [searchTerm, filterType, filterStatus]);

  const fetchWalletData = async () => {
    try {
      const response = await fetch('/api/wallet/balance', {
        credentials: 'include',
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setWalletData(data.data);
        }
      }
    } catch (error) {
      console.error('Failed to fetch wallet data:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchAllTransactions = async () => {
    try {
      setTransactionLoading(true);
      const params = new URLSearchParams();
      params.append('limit', '50');

      if (searchTerm) params.append('search', searchTerm);
      if (filterType !== 'ALL') params.append('type', filterType);
      if (filterStatus !== 'ALL') params.append('status', filterStatus);

      const response = await fetch(`/api/wallet/transactions?${params}`, {
        credentials: 'include',
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setAllTransactions(data.data.transactions);
          setTransactionTypes(data.data.filters.transactionTypes);
          setStatusOptions(data.data.filters.statusOptions);
        }
      }
    } catch (error) {
      console.error('Failed to fetch transactions:', error);
    } finally {
      setTransactionLoading(false);
    }
  };

  const handleTransactionClick = (transaction: TransactionData) => {
    setSelectedTransaction(transaction);
    setShowTransactionModal(true);
  };

  const fetchWithdrawalSettings = async () => {
    try {
      const response = await fetch('/api/wallet/withdrawal-settings', {
        credentials: 'include',
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setWithdrawalSettings(data.data);
        }
      }
    } catch (error) {
      console.error('Failed to fetch withdrawal settings:', error);
    }
  };



  const handleWithdrawal = async (e: React.FormEvent) => {
    e.preventDefault();
    setWithdrawalError('');
    setWithdrawalLoading(true);

    try {
      const amount = parseFloat(withdrawalForm.amount);
      
      if (!amount || amount <= 0) {
        throw new Error('Please enter a valid amount');
      }

      if (!withdrawalForm.usdtAddress) {
        throw new Error('Please enter a USDT address');
      }

      const response = await fetch('/api/wallet/withdraw', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          amount,
          usdtAddress: withdrawalForm.usdtAddress,
        }),
      });

      const data = await response.json();

      if (!data.success) {
        throw new Error(data.error || 'Withdrawal failed');
      }

      // Reset form and close modal
      setWithdrawalForm({ amount: '', usdtAddress: '' });
      setShowWithdrawModal(false);

      // Refresh data
      fetchWalletData();
      fetchAllTransactions();

    } catch (err: any) {
      setWithdrawalError(err.message || 'Withdrawal failed');
    } finally {
      setWithdrawalLoading(false);
    }
  };

  const getTransactionIcon = (type: string) => {
    switch (type) {
      case 'WITHDRAWAL':
        return <ArrowUpRight className="h-4 w-4 text-red-500" />;
      case 'DEPOSIT':
      case 'MINING_EARNINGS':
      case 'DIRECT_REFERRAL':
      case 'BINARY_BONUS':
        return <ArrowDownLeft className="h-4 w-4 text-eco-500" />;
      default:
        return <Wallet className="h-4 w-4 text-gray-500" />;
    }
  };

  const getTransactionColor = (type: string) => {
    switch (type) {
      case 'WITHDRAWAL':
      case 'PURCHASE':
        return 'text-red-600';
      case 'DEPOSIT':
      case 'MINING_EARNINGS':
      case 'DIRECT_REFERRAL':
      case 'BINARY_BONUS':
        return 'text-eco-600';
      default:
        return 'text-gray-600';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'COMPLETED':
      case 'APPROVED':
        return <CheckCircle className="h-4 w-4 text-eco-500" />;
      case 'PENDING':
        return <Clock className="h-4 w-4 text-solar-500" />;
      case 'FAILED':
      case 'REJECTED':
        return <XCircle className="h-4 w-4 text-red-500" />;
      default:
        return <Clock className="h-4 w-4 text-gray-500" />;
    }
  };



  const calculateWithdrawalFees = (amount: number) => {
    if (!withdrawalSettings || !amount) {
      return { fixedFee: 0, percentageFee: 0, totalFees: 0, totalDeduction: 0, netAmount: 0 };
    }

    const fixedFee = withdrawalSettings.fixedFee;
    const percentageFee = (amount * withdrawalSettings.percentageFee) / 100;
    const totalFees = fixedFee + percentageFee;
    const totalDeduction = amount + totalFees;
    const netAmount = amount;

    return { fixedFee, percentageFee, totalFees, totalDeduction, netAmount };
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-32 bg-gray-200 rounded-xl"></div>
        </div>
      </div>
    );
  }

  if (!walletData) {
    return (
      <Card>
        <CardContent className="text-center py-8">
          <p className="text-gray-500">Failed to load wallet data</p>
        </CardContent>
      </Card>
    );
  }

  const tabs = [
    { id: 'overview', label: 'Overview', icon: Wallet },
    { id: 'deposit', label: 'Deposit', icon: ArrowDownLeft },
    { id: 'withdraw', label: 'Withdraw', icon: ArrowUpRight },
  ];

  const renderTabContent = () => {
    switch (activeTab) {
      case 'deposit':
        return <DepositPage />;
      case 'withdraw':
        return renderWithdrawContent();
      default:
        return renderOverviewContent();
    }
  };

  const renderOverviewContent = () => (
    <div className="space-y-8">
      {/* Wallet Overview */}
      <div>
        <Grid cols={{ default: 1, lg: 2 }} gap={8}>
          <Card>
            <CardContent className="p-8">
              <div className="flex items-center justify-between mb-6">
                <div className="flex-1">
                  <p className="text-sm font-medium text-gray-600 mb-2">Available Balance</p>
                  <p className="text-4xl font-bold text-dark-900">
                    {formatCurrency(walletData.balance)}
                  </p>
                </div>
                <div className="h-16 w-16 bg-eco-100 rounded-xl flex items-center justify-center">
                  <Wallet className="h-8 w-8 text-eco-600" />
                </div>
              </div>
              <div className="grid grid-cols-2 gap-3">
                <Button
                  onClick={() => setActiveTab('deposit')}
                  variant="outline"
                  className="h-12 text-base font-semibold rounded-xl"
                >
                  <ArrowDownLeft className="h-5 w-5 mr-2" />
                  Deposit
                </Button>
                <Button
                  onClick={() => setActiveTab('withdraw')}
                  className="h-12 text-base font-semibold rounded-xl"
                  disabled={walletData.balance < 10}
                >
                  <ArrowUpRight className="h-5 w-5 mr-2" />
                  Withdraw
                </Button>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-8">
              <div className="flex items-center justify-between mb-6">
                <div className="flex-1">
                  <p className="text-sm font-medium text-gray-600 mb-2">Pending Earnings</p>
                  <p className="text-4xl font-bold text-solar-600">
                    {formatCurrency(walletData.pendingEarnings)}
                  </p>
                </div>
                <div className="h-16 w-16 bg-solar-100 rounded-xl flex items-center justify-center">
                  <Clock className="h-8 w-8 text-solar-600" />
                </div>
              </div>
              <p className="text-sm text-gray-600 font-medium">
                Will be transferred on Saturday at 15:00 UTC
              </p>
            </CardContent>
          </Card>
        </Grid>
      </div>

      {/* Enhanced Transactions */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>Transaction History</CardTitle>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowFilters(!showFilters)}
                className="flex items-center space-x-2"
              >
                <Filter className="h-4 w-4" />
                <span>Filters</span>
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={fetchAllTransactions}
                disabled={transactionLoading}
                className="flex items-center space-x-2"
              >
                <RefreshCw className={`h-4 w-4 ${transactionLoading ? 'animate-spin' : ''}`} />
                <span>Refresh</span>
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {/* Search and Filters */}
          <div className="space-y-4 mb-6">
            {/* Search Bar */}
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="Search transactions by description, type, or reference..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>

            {/* Filter Options */}
            {showFilters && (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 p-4 bg-gray-50 rounded-lg">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Transaction Type
                  </label>
                  <select
                    value={filterType}
                    onChange={(e) => setFilterType(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-eco-500"
                  >
                    {transactionTypes.map(type => (
                      <option key={type} value={type}>
                        {type === 'ALL' ? 'All Types' : type.replace('_', ' ')}
                      </option>
                    ))}
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Status
                  </label>
                  <select
                    value={filterStatus}
                    onChange={(e) => setFilterStatus(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-eco-500"
                  >
                    {statusOptions.map(status => (
                      <option key={status} value={status}>
                        {status === 'ALL' ? 'All Status' : status.replace('_', ' ')}
                      </option>
                    ))}
                  </select>
                </div>
              </div>
            )}
          </div>

          {/* Transaction List */}
          {transactionLoading ? (
            <div className="text-center py-8">
              <RefreshCw className="h-8 w-8 animate-spin mx-auto text-gray-400 mb-2" />
              <p className="text-gray-500">Loading transactions...</p>
            </div>
          ) : allTransactions.length > 0 ? (
            <div className="space-y-3">
              {allTransactions.map((transaction) => (
                <div
                  key={transaction.id}
                  className="flex items-center justify-between p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors cursor-pointer"
                  onClick={() => handleTransactionClick(transaction)}
                >
                  <div className="flex items-center space-x-3">
                    {getTransactionIcon(transaction.type)}
                    <div className="flex-1">
                      <p className="font-medium text-dark-900">{transaction.description}</p>
                      <div className="flex items-center space-x-4 text-sm text-gray-500">
                        <span>{formatDateTime(transaction.createdAt)}</span>
                        <span className="px-2 py-1 bg-gray-200 rounded text-xs">
                          {transaction.type.replace('_', ' ')}
                        </span>
                        {transaction.reference && (
                          <span
                            className="px-2 py-1 bg-blue-100 text-blue-700 rounded text-xs cursor-pointer hover:bg-blue-200"
                            onClick={(e) => {
                              e.stopPropagation();
                              copyToClipboard(transaction.reference!);
                            }}
                            title="Click to copy reference"
                          >
                            Ref: {transaction.reference.substring(0, 8)}...
                          </span>
                        )}
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3">
                    {getStatusIcon(transaction.status)}
                    <span className={`font-semibold ${getTransactionColor(transaction.type)}`}>
                      {transaction.type === 'WITHDRAWAL' || transaction.type === 'PURCHASE' ? '-' : '+'}
                      {formatCurrency(transaction.amount)}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8">
              <p className="text-gray-500">No transactions found</p>
              {(searchTerm || filterType !== 'ALL' || filterStatus !== 'ALL') && (
                <p className="text-sm text-gray-400 mt-2">
                  Try adjusting your search or filter criteria
                </p>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );

  const renderWithdrawContent = () => {
    const amount = parseFloat(withdrawalForm.amount) || 0;
    const feeCalculation = calculateWithdrawalFees(amount);

    return (
      <div className="space-y-8">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 mb-6">Withdraw USDT</h2>

          <Card className="max-w-3xl">
            <CardContent className="p-8">
              <form onSubmit={handleWithdrawal} className="space-y-6">
                {withdrawalError && (
                  <div className="bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-lg text-sm">
                    {withdrawalError}
                  </div>
                )}

                <div className="bg-blue-50 border border-blue-200 text-blue-700 px-4 py-3 rounded-lg text-sm">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <p className="font-medium">Available Balance</p>
                      <p className="text-lg font-bold">{formatCurrency(walletData?.balance || 0)}</p>
                    </div>
                    <div>
                      <p className="font-medium">Minimum Withdrawal</p>
                      <p className="text-lg font-bold">{formatCurrency(withdrawalSettings?.minWithdrawalAmount || 10)}</p>
                    </div>
                    <div>
                      <p className="font-medium">Network</p>
                      <p className="text-lg font-bold">USDT (TRC20)</p>
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <Input
                      label="Withdrawal Amount (USD)"
                      type="number"
                      step="0.01"
                      min={withdrawalSettings?.minWithdrawalAmount || 10}
                      max={walletData?.balance || 0}
                      value={withdrawalForm.amount}
                      onChange={(e) => setWithdrawalForm(prev => ({ ...prev, amount: e.target.value }))}
                      placeholder="Enter amount to withdraw"
                      required
                    />

                    <Input
                      label="USDT TRC20 Address"
                      type="text"
                      value={withdrawalForm.usdtAddress}
                      onChange={(e) => setWithdrawalForm(prev => ({ ...prev, usdtAddress: e.target.value }))}
                      placeholder="Enter your USDT TRC20 address"
                      required
                    />
                  </div>

                  {/* Fee Calculation Display */}
                  {amount > 0 && (
                    <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
                      <h4 className="font-medium text-gray-900 mb-3">Fee Breakdown</h4>
                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <span className="text-gray-600">Withdrawal Amount:</span>
                          <span className="font-medium">{formatCurrency(amount)}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Fixed Fee:</span>
                          <span className="font-medium">{formatCurrency(feeCalculation.fixedFee)}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Percentage Fee ({withdrawalSettings?.percentageFee || 0}%):</span>
                          <span className="font-medium">{formatCurrency(feeCalculation.percentageFee)}</span>
                        </div>
                        <div className="border-t border-gray-300 pt-2">
                          <div className="flex justify-between font-medium">
                            <span className="text-gray-900">Total Fees:</span>
                            <span className="text-red-600">{formatCurrency(feeCalculation.totalFees)}</span>
                          </div>
                          <div className="flex justify-between font-medium">
                            <span className="text-gray-900">Total Deduction:</span>
                            <span className="text-red-600">{formatCurrency(feeCalculation.totalDeduction)}</span>
                          </div>
                          <div className="flex justify-between font-bold text-lg">
                            <span className="text-gray-900">You Receive:</span>
                            <span className="text-green-600">{formatCurrency(feeCalculation.netAmount)}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                </div>

                <div className="bg-yellow-50 border border-yellow-200 text-yellow-700 px-4 py-3 rounded-lg text-sm">
                  <p><strong>Important:</strong></p>
                  <ul className="list-disc list-inside mt-1 space-y-1">
                    <li>Only USDT TRC20 addresses are supported</li>
                    <li>Withdrawals require KYC verification</li>
                    <li>Processing time: {withdrawalSettings?.processingDays || 3} business days</li>
                    <li>Double-check your address - transactions cannot be reversed</li>
                    <li>Fees are deducted from your balance in addition to the withdrawal amount</li>
                  </ul>
                </div>

                <div className="flex space-x-3">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => setActiveTab('overview')}
                    className="flex-1"
                  >
                    Back to Overview
                  </Button>
                  <Button
                    type="submit"
                    loading={withdrawalLoading}
                    className="flex-1"
                    disabled={
                      !walletData ||
                      !withdrawalSettings ||
                      amount < (withdrawalSettings?.minWithdrawalAmount || 10) ||
                      feeCalculation.totalDeduction > walletData.balance
                    }
                  >
                    {feeCalculation.totalDeduction > (walletData?.balance || 0)
                      ? 'Insufficient Balance'
                      : 'Submit Withdrawal'
                    }
                  </Button>
                </div>
              </form>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  };

  return (
    <div className="space-y-6">
      {/* Tab Navigation */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          {tabs.map((tab) => {
            const Icon = tab.icon;
            const isActive = activeTab === tab.id;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`
                  flex items-center space-x-2 py-2 px-1 border-b-2 font-medium text-sm transition-colors
                  ${isActive
                    ? 'border-solar-500 text-solar-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }
                `}
              >
                <Icon className="h-4 w-4" />
                <span>{tab.label}</span>
              </button>
            );
          })}
        </nav>
      </div>

      {/* Tab Content */}
      {renderTabContent()}

      {/* Transaction Detail Modal */}
      {showTransactionModal && selectedTransaction && (
        <Modal
          isOpen={showTransactionModal}
          onClose={() => setShowTransactionModal(false)}
          title="Transaction Details"
          size="lg"
        >
          <div className="space-y-6">
            {/* Transaction Header */}
            <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
              <div className="flex items-center space-x-3">
                {getTransactionIcon(selectedTransaction.type)}
                <div>
                  <h3 className="font-semibold text-lg text-dark-900">
                    {selectedTransaction.description}
                  </h3>
                  <p className="text-sm text-gray-600">
                    {selectedTransaction.type.replace('_', ' ')} • {formatDateTime(selectedTransaction.createdAt)}
                  </p>
                </div>
              </div>
              <div className="text-right">
                <div className="flex items-center space-x-2 mb-1">
                  {getStatusIcon(selectedTransaction.status)}
                  <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                    selectedTransaction.status === 'COMPLETED' || selectedTransaction.status === 'CONFIRMED' ? 'bg-eco-100 text-eco-700' :
                    selectedTransaction.status === 'PENDING' ? 'bg-solar-100 text-solar-700' :
                    'bg-red-100 text-red-700'
                  }`}>
                    {selectedTransaction.status}
                  </span>
                </div>
                <p className={`text-xl font-bold ${getTransactionColor(selectedTransaction.type)}`}>
                  {selectedTransaction.type === 'WITHDRAWAL' || selectedTransaction.type === 'PURCHASE' ? '-' : '+'}
                  {formatCurrency(selectedTransaction.amount)}
                </p>
              </div>
            </div>

            {/* Transaction Details */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-3">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Transaction ID</label>
                  <div className="flex items-center space-x-2">
                    <p className="text-sm text-gray-900 font-mono bg-gray-100 px-2 py-1 rounded">
                      {selectedTransaction.id}
                    </p>
                    <button
                      onClick={() => copyToClipboard(selectedTransaction.id)}
                      className="text-gray-400 hover:text-gray-600"
                    >
                      <Copy className="h-4 w-4" />
                    </button>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Type</label>
                  <p className="text-sm text-gray-900">{selectedTransaction.type.replace('_', ' ')}</p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Status</label>
                  <div className="flex items-center space-x-2">
                    {getStatusIcon(selectedTransaction.status)}
                    <span className="text-sm text-gray-900">{selectedTransaction.status}</span>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Date & Time</label>
                  <p className="text-sm text-gray-900">{formatDateTime(selectedTransaction.createdAt)}</p>
                </div>
              </div>

              <div className="space-y-3">
                {selectedTransaction.reference && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Reference</label>
                    <div className="flex items-center space-x-2">
                      <p className="text-sm text-gray-900 font-mono bg-gray-100 px-2 py-1 rounded break-all">
                        {selectedTransaction.reference}
                      </p>
                      <button
                        onClick={() => copyToClipboard(selectedTransaction.reference!)}
                        className="text-gray-400 hover:text-gray-600 flex-shrink-0"
                      >
                        <Copy className="h-4 w-4" />
                      </button>
                    </div>
                  </div>
                )}

                {selectedTransaction.txid && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Transaction Hash</label>
                    <div className="flex items-center space-x-2">
                      <p className="text-sm text-gray-900 font-mono bg-gray-100 px-2 py-1 rounded break-all">
                        {selectedTransaction.txid}
                      </p>
                      <button
                        onClick={() => copyToClipboard(selectedTransaction.txid!)}
                        className="text-gray-400 hover:text-gray-600 flex-shrink-0"
                      >
                        <Copy className="h-4 w-4" />
                      </button>
                    </div>
                  </div>
                )}

                {selectedTransaction.usdtAddress && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      {selectedTransaction.type === 'DEPOSIT' ? 'Deposit Address' : 'Withdrawal Address'}
                    </label>
                    <div className="flex items-center space-x-2">
                      <p className="text-sm text-gray-900 font-mono bg-gray-100 px-2 py-1 rounded break-all">
                        {selectedTransaction.usdtAddress}
                      </p>
                      <button
                        onClick={() => copyToClipboard(selectedTransaction.usdtAddress!)}
                        className="text-gray-400 hover:text-gray-600 flex-shrink-0"
                      >
                        <Copy className="h-4 w-4" />
                      </button>
                    </div>
                  </div>
                )}

                {selectedTransaction.confirmations !== undefined && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Confirmations</label>
                    <p className="text-sm text-gray-900">{selectedTransaction.confirmations}</p>
                  </div>
                )}

                {selectedTransaction.processedAt && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Processed At</label>
                    <p className="text-sm text-gray-900">{formatDateTime(selectedTransaction.processedAt)}</p>
                  </div>
                )}
              </div>
            </div>

            {/* Additional Information */}
            {selectedTransaction.rejectionReason && (
              <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                <h4 className="font-medium text-red-800 mb-2">Rejection Reason</h4>
                <p className="text-sm text-red-700">{selectedTransaction.rejectionReason}</p>
              </div>
            )}

            {selectedTransaction.senderAddress && (
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <h4 className="font-medium text-blue-800 mb-2">Sender Information</h4>
                <div className="flex items-center space-x-2">
                  <p className="text-sm text-blue-700 font-mono">{selectedTransaction.senderAddress}</p>
                  <button
                    onClick={() => copyToClipboard(selectedTransaction.senderAddress!)}
                    className="text-blue-400 hover:text-blue-600"
                  >
                    <Copy className="h-4 w-4" />
                  </button>
                </div>
              </div>
            )}

            {/* Action Buttons */}
            <div className="flex justify-end space-x-3 pt-4 border-t">
              <Button
                variant="outline"
                onClick={() => setShowTransactionModal(false)}
              >
                Close
              </Button>
              {selectedTransaction.txid && (
                <Button
                  onClick={() => {
                    const explorerUrl = `https://tronscan.org/#/transaction/${selectedTransaction.txid}`;
                    window.open(explorerUrl, '_blank');
                  }}
                  className="flex items-center space-x-2"
                >
                  <span>View on Explorer</span>
                  <ArrowUpRight className="h-4 w-4" />
                </Button>
              )}
            </div>
          </div>
        </Modal>
      )}
    </div>
  );
};
