import { NextRequest, NextResponse } from 'next/server';
import { authenticateRequest, isAdmin } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { systemLogDb, walletBalanceDb, adminSettingsDb, transactionDb } from '@/lib/database';

export async function POST(request: NextRequest) {
  try {
    const { authenticated, user } = await authenticateRequest(request);
    
    if (!authenticated || !user) {
      return NextResponse.json({ error: 'Not authenticated' }, { status: 401 });
    }

    const userIsAdmin = await isAdmin(user.id);
    if (!userIsAdmin) {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 });
    }

    const body = await request.json();
    const { withdrawalId, action, rejectionReason, transactionHash } = body;

    if (!withdrawalId || !action) {
      return NextResponse.json({ error: 'Withdrawal ID and action are required' }, { status: 400 });
    }

    const withdrawal = await prisma.withdrawalRequest.findUnique({
      where: { id: withdrawalId },
      include: {
        user: {
          select: { id: true, email: true, firstName: true, lastName: true },
        },
      },
    });

    if (!withdrawal) {
      return NextResponse.json({ error: 'Withdrawal request not found' }, { status: 404 });
    }

    let updatedWithdrawal;
    let logAction = '';
    let logDetails: any = {
      withdrawalId,
      userId: withdrawal.userId,
      amount: withdrawal.amount,
      userEmail: withdrawal.user.email,
    };

    switch (action) {
      case 'APPROVE':
        updatedWithdrawal = await prisma.withdrawalRequest.update({
          where: { id: withdrawalId },
          data: {
            status: 'APPROVED',
            processedBy: user.id,
            processedAt: new Date(),
          },
        });

        // Update the corresponding transaction status
        await prisma.transaction.updateMany({
          where: {
            reference: withdrawalId,
            type: 'WITHDRAWAL',
            status: 'PENDING',
          },
          data: {
            status: 'COMPLETED',
          },
        });

        logAction = 'WITHDRAWAL_APPROVED';
        break;

      case 'REJECT':
        if (!rejectionReason) {
          return NextResponse.json({ error: 'Rejection reason is required' }, { status: 400 });
        }

        // Get withdrawal settings to calculate total deduction that was made
        const fixedFee = parseFloat(await adminSettingsDb.get('withdrawalFeeFixed') || '3');
        const percentageFee = parseFloat(await adminSettingsDb.get('withdrawalFeePercentage') || '1');
        const percentageFeeAmount = (withdrawal.amount * percentageFee) / 100;
        const totalFees = fixedFee + percentageFeeAmount;
        const totalDeduction = withdrawal.amount + totalFees;

        // Restore the deducted amount back to user's wallet
        const currentWallet = await walletBalanceDb.getOrCreate(withdrawal.userId);
        await walletBalanceDb.updateBalance(withdrawal.userId, {
          availableBalance: currentWallet.availableBalance + totalDeduction,
        });

        updatedWithdrawal = await prisma.withdrawalRequest.update({
          where: { id: withdrawalId },
          data: {
            status: 'REJECTED',
            processedBy: user.id,
            processedAt: new Date(),
            rejectionReason,
          },
        });
        logAction = 'WITHDRAWAL_REJECTED';
        logDetails.rejectionReason = rejectionReason;
        logDetails.restoredAmount = totalDeduction;
        break;

      case 'COMPLETE':
        if (!transactionHash) {
          return NextResponse.json({ error: 'Transaction hash is required' }, { status: 400 });
        }
        updatedWithdrawal = await prisma.withdrawalRequest.update({
          where: { id: withdrawalId },
          data: {
            status: 'COMPLETED',
            processedBy: user.id,
            processedAt: new Date(),
            txid: transactionHash,
          },
        });
        logAction = 'WITHDRAWAL_COMPLETED';
        logDetails.transactionHash = transactionHash;
        break;

      default:
        return NextResponse.json({ error: 'Invalid action' }, { status: 400 });
    }

    // Log the admin action
    await systemLogDb.create({
      action: logAction,
      userId: user.id,
      details: logDetails,
      ipAddress: request.headers.get('x-forwarded-for') || 'unknown',
      userAgent: request.headers.get('user-agent') || 'unknown',
    });

    return NextResponse.json({
      success: true,
      message: `Withdrawal ${action.toLowerCase()}d successfully`,
      data: updatedWithdrawal,
    });

  } catch (error) {
    console.error('Admin withdrawal action error:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to perform withdrawal action' },
      { status: 500 }
    );
  }
}
