import { NextRequest, NextResponse } from 'next/server';
import { authenticateRequest } from '@/lib/auth';
import { transactionDb, depositTransactionDb } from '@/lib/database';
import { prisma } from '@/lib/prisma';

// GET - Fetch user's transactions with search and filtering
export async function GET(request: NextRequest) {
  try {
    const { authenticated, user } = await authenticateRequest(request);

    if (!authenticated || !user) {
      return NextResponse.json(
        { success: false, error: 'Not authenticated' },
        { status: 401 }
      );
    }

    // Parse query parameters
    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get('limit') || '20');
    const offset = parseInt(searchParams.get('offset') || '0');
    const search = searchParams.get('search') || '';
    const type = searchParams.get('type') || '';
    const status = searchParams.get('status') || '';
    const dateFrom = searchParams.get('dateFrom') || '';
    const dateTo = searchParams.get('dateTo') || '';

    // Build where clause for transactions
    const where: any = { userId: user.id };
    
    if (type && type !== 'ALL') {
      where.type = type;
    }
    
    if (status && status !== 'ALL') {
      where.status = status;
    }

    // Date filtering
    if (dateFrom || dateTo) {
      where.createdAt = {};
      if (dateFrom) {
        where.createdAt.gte = new Date(dateFrom);
      }
      if (dateTo) {
        where.createdAt.lte = new Date(dateTo + 'T23:59:59.999Z');
      }
    }

    // Build filters for database query
    const dbFilters: any = {
      limit: Math.min(limit, 100),
      offset,
    };

    if (search) {
      dbFilters.search = search;
    }

    if (type && type !== 'ALL') {
      dbFilters.types = [type];
    }

    if (status && status !== 'ALL') {
      dbFilters.status = status;
    }

    // Get transactions with database-level filtering
    const transactions = await transactionDb.findByUserId(user.id, dbFilters);

    // Get recent deposits for deposit transactions (only if not filtering by non-deposit type)
    let recentDeposits: any[] = [];
    if (!type || type === 'ALL' || type === 'DEPOSIT') {
      recentDeposits = await depositTransactionDb.findByUserId(user.id, {
        limit: 50,
        status: status && status !== 'ALL' ? status as any : undefined
      });

      // Filter out deposits that already exist as DEPOSIT transactions in the regular transaction table
      // to prevent duplicates
      const existingDepositRefs = new Set(
        transactions
          .filter(tx => tx.type === 'DEPOSIT' && tx.reference)
          .map(tx => tx.reference)
      );

      recentDeposits = recentDeposits.filter(deposit =>
        !existingDepositRefs.has(deposit.transactionId)
      );
    }

    // Get withdrawal requests (only if not filtering by non-withdrawal type)
    let withdrawalRequests: any[] = [];
    if (!type || type === 'ALL' || type === 'WITHDRAWAL') {
      withdrawalRequests = await prisma.withdrawalRequest.findMany({
        where: {
          userId: user.id,
          ...(status && status !== 'ALL' ? { status: status as any } : {})
        },
        orderBy: { createdAt: 'desc' },
        take: 50,
      });
    }

    // Filter deposits by search if provided
    if (search && recentDeposits.length > 0) {
      const searchLower = search.toLowerCase();
      recentDeposits = recentDeposits.filter(deposit =>
        deposit.transactionId.toLowerCase().includes(searchLower) ||
        'deposit'.includes(searchLower) ||
        'usdt'.includes(searchLower) ||
        'trc20'.includes(searchLower)
      );
    }

    // Combine and format all transactions
    const allTransactions = [
      ...transactions
        .filter(tx => tx.type !== 'ADMIN_CREDIT' && tx.type !== 'ADMIN_DEBIT') // Exclude admin transactions from user view
        .map(tx => ({
          id: tx.id,
          type: tx.type,
          category: 'TRANSACTION',
          amount: tx.amount,
          description: tx.description,
          status: tx.status,
          reference: tx.reference,
          createdAt: tx.createdAt,
          // Additional details for modal
          txid: null,
          usdtAddress: null,
          rejectionReason: null,
          processedAt: null,
        })),
      ...recentDeposits.map(deposit => ({
        id: deposit.id,
        type: 'DEPOSIT',
        category: 'DEPOSIT',
        amount: deposit.usdtAmount,
        description: `USDT TRC20 Deposit - TX: ${deposit.transactionId.substring(0, 20)}...`,
        status: deposit.status,
        reference: deposit.transactionId,
        createdAt: deposit.createdAt,
        // Additional details for modal
        txid: deposit.transactionId,
        usdtAddress: deposit.tronAddress,
        rejectionReason: null,
        processedAt: null,
        confirmations: deposit.confirmations,
        blockNumber: deposit.blockNumber,
        senderAddress: deposit.senderAddress,
      })),
      ...withdrawalRequests.map(withdrawal => ({
        id: withdrawal.id,
        type: 'WITHDRAWAL',
        category: 'WITHDRAWAL',
        amount: withdrawal.amount,
        description: `USDT TRC20 Withdrawal - ${withdrawal.amount} USDT`,
        status: withdrawal.status,
        reference: withdrawal.txid || `withdrawal_${withdrawal.id}`,
        createdAt: withdrawal.createdAt,
        // Additional details for modal
        txid: withdrawal.txid,
        usdtAddress: withdrawal.usdtAddress,
        rejectionReason: withdrawal.rejectionReason,
        processedAt: withdrawal.processedAt,
      }))
    ];

    // Sort by creation date (newest first)
    allTransactions.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());

    // Apply pagination to combined results
    const paginatedTransactions = allTransactions.slice(offset, offset + limit);

    // Get transaction type options for filtering (exclude admin types from user view)
    const transactionTypes = [
      'ALL',
      'MINING_EARNINGS',
      'DIRECT_REFERRAL',
      'BINARY_BONUS',
      'DEPOSIT',
      'WITHDRAWAL',
      'PURCHASE'
    ];

    const statusOptions = [
      'ALL',
      'PENDING',
      'COMPLETED',
      'FAILED',
      'CANCELLED',
      'CONFIRMED',
      'PENDING_VERIFICATION',
      'WAITING_FOR_CONFIRMATIONS'
    ];

    return NextResponse.json({
      success: true,
      data: {
        transactions: paginatedTransactions,
        pagination: {
          limit,
          offset,
          total: allTransactions.length,
          hasMore: offset + limit < allTransactions.length,
        },
        filters: {
          transactionTypes,
          statusOptions,
        },
      },
    });

  } catch (error: any) {
    console.error('Wallet transactions fetch error:', error);
    
    return NextResponse.json(
      { success: false, error: 'Failed to fetch wallet transactions' },
      { status: 500 }
    );
  }
}
